﻿#pragma checksum "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AE246E052EE2DD6C77319D864D0928AA418EF435"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// ProfessionalPrintPreviewWindow
    /// </summary>
    public partial class ProfessionalPrintPreviewWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 83 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ZoomComboBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrentPageTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPagesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PrinterComboBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaperSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OrientationComboBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PageRangeComboBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomRangeTextBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CopiesTextBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ThumbnailsPanel;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MainPreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MainPreviewPanel;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ZoomLevelTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/professionalprintpreviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 70 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 72 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 82 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ZoomOutButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ZoomComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 84 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.ZoomComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ZoomComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 95 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ZoomInButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 104 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SinglePageView_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 106 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TwoPageView_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 108 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MultiPageView_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 117 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FirstPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 119 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CurrentPageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 122 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.CurrentPageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CurrentPageTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TotalPagesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 126 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 128 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LastPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 136 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PrinterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 148 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.PrinterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PrinterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 150 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshPrintersButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.PaperSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 157 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.PaperSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaperSizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.OrientationComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 169 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.OrientationComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.OrientationComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PageRangeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 179 "..\..\..\..\Views\ProfessionalPrintPreviewWindow.xaml"
            this.PageRangeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PageRangeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.CustomRangeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.CopiesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.ThumbnailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 24:
            this.MainPreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 25:
            this.MainPreviewPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 26:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.PageInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.ZoomLevelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

