using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace DriverManagementSystem.Models
{
    public class ReportModel : INotifyPropertyChanged
    {
        // معلومات التقرير الأساسية
        public string ReportDate { get; set; } = DateTime.Now.ToString("dd/MM/yyyy");
        public string VisitNumber { get; set; } = string.Empty;
        public string ReportTitle { get; set; } = "محضر استخراج عروض أسعار";
        public string OrganizationName { get; set; } = "الجمهورية اليمنية";
        public string DepartmentName { get; set; } = "المجلس المحلي للمديرية";
        public string BranchName { get; set; } = "فرع عدن والمحافظات";

        // خصائص التاريخ واليوم للعقد
        public string ContractDate { get; set; } = DateTime.Now.ToString("dd/MM/yyyy");
        public string ContractDayName { get; set; } = "الأحد";
        public string StartDateArabic { get; set; } = "الأحد";
        public string EndDateArabic { get; set; } = "الثلاثاء";

        // بيانات الزيارة
        public string MissionPurpose { get; set; } = string.Empty;
        public string VisitNature { get; set; } = "زيارة استطلاعية لمكونات المشروع";
        public string VisitConductor { get; set; } = string.Empty; // للتقارير بدون صفة
        public string VisitConductorWithRank { get; set; } = string.Empty; // للعقد مع الصفة

        // خاصية منسقة للقائمين بالزيارة مع فاصل &
        public string VisitConductorFormatted
        {
            get
            {
                try
                {
                    if (string.IsNullOrEmpty(VisitConductor))
                        return "القائم بالزيارة";

                    // إذا كان النص طويل، نضع فاصل & ونقسمه على سطرين
                    if (VisitConductor.Length > 30 && VisitConductor.Contains(" "))
                    {
                        var parts = VisitConductor.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 4)
                        {
                            var midPoint = parts.Length / 2;
                            var firstPart = string.Join(" ", parts.Take(midPoint));
                            var secondPart = string.Join(" ", parts.Skip(midPoint));
                            return $"{firstPart} &\n{secondPart}";
                        }
                    }

                    return VisitConductor;
                }
                catch
                {
                    return VisitConductor ?? "القائم بالزيارة";
                }
            }
        }


        public string RouteDescription { get; set; } = string.Empty;
        public string MessageText { get; set; } = string.Empty;
        public string WinnerDriverMessage { get; set; } = string.Empty; // نص الرسالة للسائق الفائز

        // خصائص للنصوص المتغيرة (آمنة)
        public string VisitConductorPrefix { get; set; } = "لمرافقة الأخ/";
        public string VisitConductorSuffix { get; set; } = " أثناء تنفيذ المهام الميدانية المطلوبة منه .";
        public string RentalDurationText { get; set; } = "ولمدة يوم";
        public string FirstPartyName { get; set; } = "الطرف الأول";
        public string DepartureDate { get; set; } = string.Empty;
        public string ReturnDate { get; set; } = string.Empty;
        public string StartDate { get; set; } = string.Empty; // تاريخ البداية للاستمارة
        public string EndDate { get; set; } = string.Empty; // تاريخ النهاية للاستمارة
        public int DaysCount { get; set; }
        public string SectorName { get; set; } = string.Empty; // القطاع
        public string Notes { get; set; } = string.Empty; // ملاحظات
        public int VisitorsCount { get; set; } // عدد القائمين بالزيارة

        // الحقول الجديدة من Excel
        public string ApprovalBy { get; set; } = string.Empty; // الموافقة على السفر
        public DateTime? SubmissionTime { get; set; } // وقت وتاريخ الإرسال
        public string FormattedSubmissionTime { get; set; } = string.Empty; // وقت الإرسال منسق

        // القائمين بالزيارة
        private ObservableCollection<VisitorReportItem> _visitors = new ObservableCollection<VisitorReportItem>();
        public ObservableCollection<VisitorReportItem> Visitors
        {
            get => _visitors;
            set
            {
                _visitors = value;
                OnPropertyChanged();
            }
        }

        // خط السير
        private ObservableCollection<ItineraryReportItem> _itinerary = new ObservableCollection<ItineraryReportItem>();
        public ObservableCollection<ItineraryReportItem> Itinerary
        {
            get => _itinerary;
            set
            {
                _itinerary = value;
                OnPropertyChanged();
            }
        }

        // المشاريع
        private ObservableCollection<ProjectReportItem> _projects = new ObservableCollection<ProjectReportItem>();
        public ObservableCollection<ProjectReportItem> Projects
        {
            get => _projects;
            set
            {
                _projects = value;
                OnPropertyChanged();
            }
        }

        // عروض الأسعار
        private ObservableCollection<PriceOfferItem> _priceOffers = new ObservableCollection<PriceOfferItem>();
        public ObservableCollection<PriceOfferItem> PriceOffers
        {
            get => _priceOffers;
            set
            {
                _priceOffers = value;
                OnPropertyChanged();
            }
        }

        // بيانات السائق الفائز
        private WinnerDriverInfo _winnerDriver = new WinnerDriverInfo();
        public WinnerDriverInfo WinnerDriver
        {
            get => _winnerDriver;
            set
            {
                _winnerDriver = value;
                OnPropertyChanged();
                UpdateWinnerPriceText(); // تحديث النص العربي
            }
        }

        /// <summary>
        /// المبلغ الفائز كنص عربي
        /// </summary>
        private string _winnerPriceInArabicText = "غير محدد";
        public string WinnerPriceInArabicText
        {
            get => _winnerPriceInArabicText;
            set
            {
                _winnerPriceInArabicText = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تحديث النص العربي للمبلغ
        /// </summary>
        public void UpdateWinnerPriceText()
        {
            try
            {
                if (WinnerDriver?.WinningPrice > 0)
                {
                    WinnerPriceInArabicText = Helpers.NumberToArabicTextHelper.ConvertSimple(WinnerDriver.WinningPrice);
                }
                else
                {
                    WinnerPriceInArabicText = "غير محدد";
                }
            }
            catch
            {
                WinnerPriceInArabicText = "غير محدد";
            }
        }

        // نص قرار الإرساء
        public string AwardDecisionText { get; set; } = "بناءً على الأسعار المقدمة من الإخوة أصحاب المركبات الذي تم التواصل معهم أعلاه، فقد تم إرساء النقل على الأخ";

        // بيانات السيارة والسائق (للتوافق مع الكود القديم)
        public string SelectedDriverName { get; set; } = string.Empty;
        public string SelectedDriverPhone { get; set; } = string.Empty;
        public string VehicleType { get; set; } = "فورتشن";
        public string VehicleModel { get; set; } = "2009";
        public string VehicleColor { get; set; } = "بني";
        public string PlateNumber { get; set; } = "3-1648";
        public string LicenseNumber { get; set; } = "17010173930";
        public string IssueDate { get; set; } = "17/01/2015";
        public string IssueLocation { get; set; } = "مركز 1 عدن";
        public string DriverCapacity { get; set; } = "6 سائقين";

        // التوقيعات
        public string TaskManagerName { get; set; } = "عبدالله علي ناصر الأضرعي";
        public string MovementResponsibleName { get; set; } = "علي أحمد العبدي";
        public string BranchManagerName { get; set; } = "م/ محمد محمد البليدي";
        public string BranchManagerTitle { get; set; } = "مدير الفرع";

        // صور التوثيق
        public string DocumentationImage1 { get; set; } = string.Empty;
        public string DocumentationImage2 { get; set; } = string.Empty;
        public string DocumentationImage3 { get; set; } = string.Empty;
        public string DocumentationImage4 { get; set; } = string.Empty;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ProjectReportItem
    {
        public int SerialNumber { get; set; }
        public string ProjectNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
    }

    public class PriceOfferItem
    {
        public int SerialNumber { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public decimal OfferedPrice { get; set; }
        public string Status { get; set; } = "موافق";
        public bool IsWinner { get; set; } = false;
    }

    public class VisitorReportItem
    {
        public string Name { get; set; } = string.Empty;
        public string Rank { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }

    public class ItineraryReportItem
    {
        public int DayNumber { get; set; }
        public string Plan { get; set; } = string.Empty;
    }

    public class SelectedVehicleData
    {
        public string DriverName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string VehicleModel { get; set; } = string.Empty;
        public string VehicleColor { get; set; } = string.Empty;
        public string PlateNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// بيانات السائق الفائز الكاملة
    /// </summary>
    public class WinnerDriverInfo
    {
        // بيانات السائق الشخصية
        public string DriverName { get; set; } = "غير محدد";
        public string NationalId { get; set; } = "غير محدد";
        public string CardType { get; set; } = "غير محدد"; // نوع البطاقة
        public string CardIssueDate { get; set; } = "غير محدد"; // تاريخ إصدار البطاقة
        public string CardIssuePlace { get; set; } = "غير محدد"; // مكان إصدار البطاقة
        public string IssueDate { get; set; } = "غير محدد";
        public string IssueLocation { get; set; } = "غير محدد";
        public string PhoneNumber { get; set; } = "غير محدد";
        public string DriverRank { get; set; } = "غير محدد"; // الصف

        // بيانات المركبة
        public string VehicleNumber { get; set; } = "غير محدد";
        public string VehicleType { get; set; } = "غير محدد";
        public string VehicleCapacity { get; set; } = "غير محدد"; // قدرة السيارة
        public string VehicleColor { get; set; } = "غير محدد";
        public string ManufactureYear { get; set; } = "غير محدد"; // سنة الصنع

        // بيانات الرخصة
        public string LicenseNumber { get; set; } = "غير محدد"; // رقم الرخصة
        public string LicenseIssueDate { get; set; } = "غير محدد"; // تاريخ إصدار الرخصة

        // بيانات العرض
        public decimal WinningPrice { get; set; } = 0;
        public string ContractStatus { get; set; } = "مقبول";

        /// <summary>
        /// تحديث البيانات من كائن السائق
        /// </summary>
        public void UpdateFromDriver(Driver driver)
        {
            if (driver == null) return;

            // البيانات الشخصية
            DriverName = driver.Name ?? "غير محدد";
            NationalId = driver.CardNumber ?? "غير محدد";
            CardType = driver.CardType ?? "بطاقة شخصية";
            PhoneNumber = driver.PhoneNumber ?? "غير محدد";

            // تواريخ ومكان الإصدار
            if (driver.CardIssueDate != default(DateTime))
            {
                IssueDate = driver.CardIssueDate.ToString("dd/MM/yyyy");
                CardIssueDate = driver.CardIssueDate.ToString("dd/MM/yyyy");
            }

            IssueLocation = driver.CardIssuePlace ?? "غير محدد";
            CardIssuePlace = driver.CardIssuePlace ?? "غير محدد";

            // بيانات المركبة
            VehicleNumber = driver.VehicleNumber ?? "غير محدد";
            VehicleType = driver.VehicleType ?? "غير محدد";
            VehicleColor = driver.VehicleColor ?? "غير محدد";
            VehicleCapacity = driver.VehicleCapacity ?? EstimateVehicleCapacity(driver.VehicleType);

            // بيانات الرخصة
            LicenseNumber = driver.LicenseNumber ?? "غير محدد";
            if (driver.LicenseIssueDate != default(DateTime))
            {
                LicenseIssueDate = driver.LicenseIssueDate.ToString("dd/MM/yyyy");
            }
            else
            {
                LicenseIssueDate = "غير محدد";
            }

            // استخراج سنة الصنع من الموديل أو نوع المركبة
            if (!string.IsNullOrWhiteSpace(driver.VehicleModel))
            {
                ManufactureYear = driver.VehicleModel;
            }
            else if (!string.IsNullOrWhiteSpace(driver.VehicleType))
            {
                var yearMatch = System.Text.RegularExpressions.Regex.Match(driver.VehicleType, @"\b(19|20)\d{2}\b");
                if (yearMatch.Success)
                {
                    ManufactureYear = yearMatch.Value;
                }
            }

            // إضافة معلومات افتراضية للحقول المفقودة
            if (string.IsNullOrWhiteSpace(IssueLocation))
            {
                IssueLocation = "عدن"; // مكان افتراضي
            }

            if (string.IsNullOrWhiteSpace(DriverRank))
            {
                DriverRank = "سائق"; // رتبة افتراضية
            }
        }

        /// <summary>
        /// تقدير قدرة السيارة بناءً على النوع
        /// </summary>
        private string EstimateVehicleCapacity(string vehicleType)
        {
            if (string.IsNullOrWhiteSpace(vehicleType))
                return "غير محدد";

            var type = vehicleType.ToLower();

            if (type.Contains("هايلكس") || type.Contains("hilux"))
                return "5 أشخاص + حمولة";
            else if (type.Contains("باترول") || type.Contains("patrol"))
                return "7 أشخاص";
            else if (type.Contains("رينجر") || type.Contains("ranger"))
                return "5 أشخاص + حمولة";
            else if (type.Contains("كولورادو") || type.Contains("colorado"))
                return "5 أشخاص + حمولة";
            else if (type.Contains("ديماكس") || type.Contains("dmax"))
                return "5 أشخاص + حمولة";
            else if (type.Contains("نافارا") || type.Contains("navara"))
                return "5 أشخاص + حمولة";
            else if (type.Contains("فورتشن") || type.Contains("fortuner"))
                return "7 أشخاص";
            else if (type.Contains("برادو") || type.Contains("prado"))
                return "7 أشخاص";
            else
                return "5 أشخاص";
        }

        /// <summary>
        /// تحديث البيانات من عرض السعر
        /// </summary>
        public void UpdateFromPriceOffer(PriceOfferItem offer)
        {
            if (offer == null) return;

            if (string.IsNullOrWhiteSpace(DriverName) || DriverName == "غير محدد")
                DriverName = offer.DriverName;

            if (string.IsNullOrWhiteSpace(PhoneNumber) || PhoneNumber == "غير محدد")
                PhoneNumber = offer.PhoneNumber;

            WinningPrice = offer.OfferedPrice;
            ContractStatus = offer.Status;
        }

        /// <summary>
        /// التحقق من اكتمال البيانات
        /// </summary>
        public bool IsDataComplete()
        {
            return !string.IsNullOrWhiteSpace(DriverName) && DriverName != "غير محدد" &&
                   !string.IsNullOrWhiteSpace(PhoneNumber) && PhoneNumber != "غير محدد" &&
                   WinningPrice > 0;
        }

        /// <summary>
        /// الحصول على ملخص البيانات
        /// </summary>
        public string GetDataSummary()
        {
            return $"السائق: {DriverName} | التلفون: {PhoneNumber} | السيارة: {VehicleType} {VehicleNumber} | الرخصة: {LicenseNumber} ({LicenseIssueDate}) | السعر: {WinningPrice:N0}";
        }
    }
}
